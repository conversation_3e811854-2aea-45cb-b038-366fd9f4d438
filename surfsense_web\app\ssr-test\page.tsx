"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Server, 
  Monitor, 
  Info, 
  ArrowLeft,
  Clock,
  Globe
} from 'lucide-react';
import { getSSRConfig, useSSRConfig } from '@/lib/ssr-config';
import { useIsMounted } from '@/components/ClientSideWrapper';
import Link from 'next/link';

/**
 * SSR Test Page
 * 
 * This page demonstrates the SSR configuration and shows how the application
 * behaves in different rendering modes.
 */
export default function SSRTestPage() {
  const config = useSSRConfig();
  const isMounted = useIsMounted();
  const [renderTime] = React.useState(() => new Date().toISOString());
  const [clientTime, setClientTime] = React.useState<string>('');

  React.useEffect(() => {
    setClientTime(new Date().toISOString());
  }, []);

  const getModeColor = (mode: string) => {
    switch (mode) {
      case 'ssr':
        return 'bg-blue-500/10 text-blue-700 border-blue-200 dark:text-blue-300 dark:border-blue-800';
      case 'csr':
        return 'bg-green-500/10 text-green-700 border-green-200 dark:text-green-300 dark:border-green-800';
      case 'static':
        return 'bg-purple-500/10 text-purple-700 border-purple-200 dark:text-purple-300 dark:border-purple-800';
      default:
        return 'bg-gray-500/10 text-gray-700 border-gray-200 dark:text-gray-300 dark:border-gray-800';
    }
  };

  return (
    <div className="container mx-auto py-10 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/dashboard">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">SSR Configuration Test</h1>
          <p className="text-muted-foreground">
            Test and verify server-side rendering configuration
          </p>
        </div>
      </div>

      {/* Current Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="h-5 w-5" />
            Current Configuration
          </CardTitle>
          <CardDescription>
            Active SSR configuration and rendering mode
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="font-medium">Rendering Mode</span>
            <Badge className={`flex items-center gap-2 ${getModeColor(config.mode)}`}>
              {config.mode === 'ssr' && <Server className="h-4 w-4" />}
              {config.mode === 'csr' && <Monitor className="h-4 w-4" />}
              {config.mode === 'static' && <Globe className="h-4 w-4" />}
              {config.mode.toUpperCase()}
            </Badge>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 rounded-lg border bg-muted/50">
              <div className="text-sm text-muted-foreground">SSR Disabled</div>
              <div className="font-medium">
                {config.disabled ? 'Yes' : 'No'}
              </div>
            </div>
            <div className="text-center p-3 rounded-lg border bg-muted/50">
              <div className="text-sm text-muted-foreground">Client Only</div>
              <div className="font-medium">
                {config.clientSideOnly ? 'Yes' : 'No'}
              </div>
            </div>
            <div className="text-center p-3 rounded-lg border bg-muted/50">
              <div className="text-sm text-muted-foreground">Is Server</div>
              <div className="font-medium">
                {config.isServer ? 'Yes' : 'No'}
              </div>
            </div>
            <div className="text-center p-3 rounded-lg border bg-muted/50">
              <div className="text-sm text-muted-foreground">Is Mounted</div>
              <div className="font-medium">
                {isMounted ? 'Yes' : 'No'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Render Timing */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Render Timing
          </CardTitle>
          <CardDescription>
            Compare server and client render times
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 rounded-lg border bg-muted/50">
              <div className="text-sm text-muted-foreground mb-1">Initial Render Time</div>
              <div className="font-mono text-sm">{renderTime}</div>
            </div>
            <div className="p-4 rounded-lg border bg-muted/50">
              <div className="text-sm text-muted-foreground mb-1">Client Hydration Time</div>
              <div className="font-mono text-sm">{clientTime || 'Not hydrated yet'}</div>
            </div>
          </div>

          {config.disabled && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                SSR is disabled. This page is rendered entirely on the client side.
                The initial render time and client hydration time should be very close.
              </AlertDescription>
            </Alert>
          )}

          {!config.disabled && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                SSR is enabled. This page is first rendered on the server, then hydrated on the client.
                You may notice a difference between initial render and hydration times.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Environment Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Environment Information
          </CardTitle>
          <CardDescription>
            Current environment and configuration details
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 rounded-lg border bg-muted/50">
              <div className="text-sm text-muted-foreground mb-1">Node Environment</div>
              <div className="font-mono text-sm">{process.env.NODE_ENV || 'development'}</div>
            </div>
            <div className="p-4 rounded-lg border bg-muted/50">
              <div className="text-sm text-muted-foreground mb-1">DISABLE_SSR Setting</div>
              <div className="font-mono text-sm">
                {process.env.NEXT_PUBLIC_DISABLE_SSR || 'not set'}
              </div>
            </div>
          </div>

          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              To change the SSR configuration, set the <code>NEXT_PUBLIC_DISABLE_SSR</code> environment 
              variable to <code>true</code> or <code>false</code> and restart the application.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Test Results */}
      <Card>
        <CardHeader>
          <CardTitle>Test Results</CardTitle>
          <CardDescription>
            Verification of SSR configuration behavior
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 rounded-lg border">
              <span>Component mounted successfully</span>
              <Badge variant={isMounted ? "default" : "destructive"}>
                {isMounted ? "✓ Pass" : "✗ Fail"}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 rounded-lg border">
              <span>Configuration loaded correctly</span>
              <Badge variant="default">✓ Pass</Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 rounded-lg border">
              <span>Environment variables accessible</span>
              <Badge variant="default">✓ Pass</Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 rounded-lg border">
              <span>Client-side hydration working</span>
              <Badge variant={clientTime ? "default" : "secondary"}>
                {clientTime ? "✓ Pass" : "⏳ Pending"}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
