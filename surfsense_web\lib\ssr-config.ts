/**
 * SSR Configuration Utility
 * 
 * This module provides utilities for managing server-side rendering (SSR) configuration
 * in the SurfSense application. It allows users to toggle between SSR and client-side
 * rendering (CSR) modes based on environment variables or runtime configuration.
 */

/**
 * Check if <PERSON> is disabled based on environment variables
 * @returns {boolean} True if <PERSON> is disabled, false otherwise
 */
export function isSSRDisabled(): boolean {
  // Check environment variable
  const disableSSR = process.env.NEXT_PUBLIC_DISABLE_SSR;
  
  // Convert string to boolean - 'true' (case insensitive) disables SSR
  return disableSSR?.toLowerCase() === 'true';
}

/**
 * Check if we're running in client-side only mode
 * @returns {boolean} True if running client-side only
 */
export function isClientSideOnly(): boolean {
  return typeof window !== 'undefined' && isSSRDisabled();
}

/**
 * Check if we're running on the server
 * @returns {boolean} True if running on server
 */
export function isServer(): boolean {
  return typeof window === 'undefined';
}

/**
 * Get the current rendering mode
 * @returns {'ssr' | 'csr' | 'static'} Current rendering mode
 */
export function getRenderingMode(): 'ssr' | 'csr' | 'static' {
  if (isSSRDisabled()) {
    return 'csr';
  }
  
  // Check if we're in static export mode (output: 'export' in next.config.ts)
  if (process.env.NODE_ENV === 'production' && !process.env.NEXT_RUNTIME) {
    return 'static';
  }
  
  return 'ssr';
}

/**
 * Configuration object for SSR settings
 */
export interface SSRConfig {
  /** Whether SSR is disabled */
  disabled: boolean;
  /** Current rendering mode */
  mode: 'ssr' | 'csr' | 'static';
  /** Whether we're running client-side only */
  clientSideOnly: boolean;
  /** Whether we're running on the server */
  isServer: boolean;
}

/**
 * Get complete SSR configuration
 * @returns {SSRConfig} Complete SSR configuration object
 */
export function getSSRConfig(): SSRConfig {
  return {
    disabled: isSSRDisabled(),
    mode: getRenderingMode(),
    clientSideOnly: isClientSideOnly(),
    isServer: isServer(),
  };
}

/**
 * Log SSR configuration for debugging
 */
export function logSSRConfig(): void {
  if (process.env.NODE_ENV === 'development') {
    const config = getSSRConfig();
    console.log('🔧 SSR Configuration:', {
      ...config,
      environment: process.env.NODE_ENV,
      disableSSREnv: process.env.NEXT_PUBLIC_DISABLE_SSR,
    });
  }
}

/**
 * Hook to get SSR configuration in React components
 * @returns {SSRConfig} SSR configuration object
 */
export function useSSRConfig(): SSRConfig {
  return getSSRConfig();
}
